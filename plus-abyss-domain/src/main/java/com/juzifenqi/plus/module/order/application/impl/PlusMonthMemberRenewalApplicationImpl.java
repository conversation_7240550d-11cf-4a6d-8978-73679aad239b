package com.juzifenqi.plus.module.order.application.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.common.entity.PlusMemberBlackEntity;
import com.juzifenqi.plus.module.order.application.IPlusMonthMemberRenewalApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderAo;
import com.juzifenqi.plus.module.order.model.contract.IPlusMonthMemberRenewalPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 会员月卡续费应用服务实现类
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Service
@Slf4j
public class PlusMonthMemberRenewalApplicationImpl implements IPlusMonthMemberRenewalApplication {

    @Autowired
    private IPlusMonthMemberRenewalPlanRepository renewalPlanRepository;

    @Autowired
    private IPlusOrderApplication plusOrderApplication;

    @Autowired
    private IPlusOrderRepository plusOrderRepository;

    @Autowired
    private IPlusBlackRepository plusBlackRepository;



    @Override
    public void createRenewalOrdersBySchedule() {
        log.info("开始执行会员月卡续费订单定时任务");
        
        try {
            // 获取当天日期
            Date today = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
            
            // 查询当天待生成的续费计划
            List<Integer> pendingStates = Arrays.asList(PlusMonthRenewalPlanStateEnum.PENDING.getCode(), PlusMonthRenewalPlanStateEnum.FAILED.getCode());
            
            int pageNum = 1;
            int pageSize = 100;
            int totalProcessed = 0;
            
            while (true) {
                List<PlusMonthMemberRenewalPlanEntity> renewalPlans = renewalPlanRepository.pageQuery(
                        today, pendingStates, pageNum, pageSize);
                
                if (CollectionUtils.isEmpty(renewalPlans)) {
                    break;
                }
                
                log.info("查询到第{}页待生成续费计划{}条", pageNum, renewalPlans.size());
                
                // 批量处理续费订单创建
                createRenewalOrders(renewalPlans);
                
                totalProcessed += renewalPlans.size();
                pageNum++;
                
                // 如果查询结果少于页大小，说明已经是最后一页
                if (renewalPlans.size() < pageSize) {
                    break;
                }
            }
            
            log.info("会员月卡续费订单定时任务执行完成，共处理{}条续费计划", totalProcessed);
            
        } catch (Exception e) {
            log.error("会员月卡续费订单定时任务执行异常", e);
        }
    }

    @Override
    public Boolean createRenewalOrder(PlusMonthMemberRenewalPlanEntity renewalPlan) {
        if (renewalPlan == null) {
            log.warn("续费计划为空，跳过处理");
            return false;
        }

        log.info("开始创建续费订单，续费计划ID: {},月卡编号{}, 用户ID: {}, 期数: {}",
                renewalPlan.getId(), renewalPlan.getMonthNo(), renewalPlan.getUserId(), renewalPlan.getCurrentPeriod());

        // 如果上期订单未支付，则将待生成续费计划改为作废，并将uid加入会员月卡黑名单
        boolean previousOrderPaid = previousOrderPaid(renewalPlan);
        if (!previousOrderPaid) {
            return false;
        }

        try {
            // 更新续费计划状态为"生成中"
            renewalPlanRepository.updatePlanState(renewalPlan.getId(), PlusMonthRenewalPlanStateEnum.GENERATING.getCode(), "开始生成续费订单");
            
            // 构建订单创建事件
            PlusOrderCreateEvent orderCreateEvent = buildOrderCreateEvent(renewalPlan);
            
            // 调用订单创建服务
            PlusOrderAo orderResult = plusOrderApplication.createPlusOrder(orderCreateEvent);
            
            if (orderResult != null && orderResult.getOrderSn() != null) {
                // 同时更新续费计划状态为"已生成"、记录订单号和实际生成时间
                renewalPlanRepository.updatePlanStateAndActualTime(renewalPlan.getId(), orderResult.getOrderSn(),
                        PlusMonthRenewalPlanStateEnum.GENERATED.getCode(), "续费订单创建成功", new Date());

                log.info("续费订单创建成功，续费计划ID: {}, 订单号: {}", renewalPlan.getId(), orderResult.getOrderSn());
                return true;
            } else {
                // 订单创建失败
                renewalPlanRepository.updatePlanState(renewalPlan.getId(), PlusMonthRenewalPlanStateEnum.FAILED.getCode(), "订单创建失败，返回结果为空");
                log.error("续费订单创建失败，续费计划ID: {}, 返回结果为空", renewalPlan.getId());
                return false;
            }
            
        } catch (Exception e) {
            // 更新续费计划状态为"生成失败"
            String errorMsg = "订单创建异常: " + e.getMessage();
            renewalPlanRepository.updatePlanState(renewalPlan.getId(), PlusMonthRenewalPlanStateEnum.FAILED.getCode(), errorMsg);
            
            log.error("续费订单创建异常，续费计划ID: {}, 异常信息: {}", renewalPlan.getId(), e.getMessage(), e);
            return false;
        }
    }

    private boolean previousOrderPaid(PlusMonthMemberRenewalPlanEntity renewalPlan) {
        if (renewalPlan.getCurrentPeriod() != null && renewalPlan.getCurrentPeriod() > 1) {
            // 查询上一期续费计划
            Integer previousPeriod = renewalPlan.getCurrentPeriod() - 1;
            PlusMonthMemberRenewalPlanEntity previousPlan = renewalPlanRepository.getPreviousPlan(
                    renewalPlan.getMonthNo(), previousPeriod);
            if (previousPlan.getOrderSn() == null) {
                //增加报错日志
                log.error("续费计划未查询到上期订单号，续费计划ID: {}, 月卡编号: {}, 用户ID: {}, 期数: {}",
                        renewalPlan.getId(), renewalPlan.getMonthNo(), renewalPlan.getUserId(), renewalPlan.getCurrentPeriod());
                return false;
            }
            // 查询上期订单的支付状态
            PlusOrderEntity previousOrder = plusOrderRepository.getByPlusOrderSn(previousPlan.getOrderSn());
            if (previousOrder != null && PlusOrderStateEnum.WAIT_PAY.getCode() == previousOrder.getOrderState()) {
                log.info("上期订单未支付，将当前续费计划作废并加入黑名单，续费计划ID: {}, 上期订单号: {}, 用户ID: {}",
                        renewalPlan.getId(), previousOrder.getOrderSn(), renewalPlan.getUserId());

                // 将当前续费计划状态修改为"作废"
                renewalPlanRepository.updatePlanState(renewalPlan.getId(),
                        PlusMonthRenewalPlanStateEnum.CANCELLED.getCode(),
                        "上期订单未支付，自动作废");

                // 将用户添加到会员月卡黑名单
                PlusMemberBlackEntity blackEntity = new PlusMemberBlackEntity();
                blackEntity.setUserId(renewalPlan.getUserId());
                blackEntity.setChannelId(renewalPlan.getChannelId());
                blackEntity.setConfigId(previousOrder.getConfigId());
                blackEntity.setBlackType(BlackListTypeEnum.BLACK_TYPE_4.getCode());
                blackEntity.setRemark("会员月卡上期订单未支付，自动加入黑名单");
                plusBlackRepository.saveBlackRecord(blackEntity);
                log.info("用户已加入会员月卡黑名单，用户ID: {}, 月卡编号: {}",
                        renewalPlan.getUserId(), renewalPlan.getMonthNo());

                // 终止当前的创建订单任务流程
                return false;
            }
        }
        return true;
    }

    @Override
    public void createRenewalOrders(List<PlusMonthMemberRenewalPlanEntity> renewalPlans) {
        if (CollectionUtils.isEmpty(renewalPlans)) {
            log.info("续费计划列表为空，跳过处理");
            return;
        }
        
        log.info("开始批量创建续费订单，续费计划数量: {}", renewalPlans.size());
        
        int successCount = 0;
        int failCount = 0;
        
        for (PlusMonthMemberRenewalPlanEntity renewalPlan : renewalPlans) {
            try {
                Boolean result = createRenewalOrder(renewalPlan);
                if (Boolean.TRUE.equals(result)) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
                log.error("处理续费计划异常，续费计划ID: {}, 异常信息: {}", renewalPlan.getId(), e.getMessage(), e);
            }
        }
        
        log.info("批量创建续费订单完成，成功: {}条, 失败: {}条", successCount, failCount);
    }

    /**
     * 构建订单创建事件
     *
     * @param renewalPlan 续费计划
     * @return 订单创建事件
     */
    private PlusOrderCreateEvent buildOrderCreateEvent(PlusMonthMemberRenewalPlanEntity renewalPlan) {
        PlusOrderCreateEvent event = new PlusOrderCreateEvent();
        
        // 基本信息
        event.setUserId(renewalPlan.getUserId());
        event.setChannelId(renewalPlan.getChannelId());
        event.setProgramId(renewalPlan.getProgramId());
        
        // 续费订单标识
        event.setRenew(OrderRenewEnum.RENEW.getCode());
        
        // 支付方式设置为后付款
        event.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        
        // 设置期数（从续费计划中获取）
        event.setMonthPeriod(renewalPlan.getCurrentPeriod());
        
        // 分流主体ID
        event.getCreateOrderContext().setShuntSupplierId(renewalPlan.getSupplierId());

        log.info("构建续费订单创建事件完成: {}", JSON.toJSONString(event));
        
        return event;
    }
}
